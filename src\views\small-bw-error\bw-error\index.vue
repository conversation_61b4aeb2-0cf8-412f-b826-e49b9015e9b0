<template>
  <el-card>
    <div class="flex-container">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="管理IP">
          <el-input v-model.trim="searchForm.ip" placeholder="多个英文逗号分隔" clearable></el-input>
        </el-form-item>
        <el-form-item label="省份">
          <el-select v-model="searchForm.province" placeholder="请选择省份" clearable @focus="loadProvinceOptions"
            :loading="provinceLoading">
            <el-option v-for="item in provinceOptions" :key="item.value" :label="item.label"
              :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="供应商">
          <el-select v-model="searchForm.supplier_uuid" placeholder="请选择供应商" clearable @focus="loadSupplierOptions"
            :loading="supplierLoading">
            <el-option v-for="item in supplierOptions" :key="item.value" :label="item.label"
              :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="运营商">
          <el-select v-model="searchForm.isp" placeholder="请选择运营商" clearable @focus="loadIspOptions"
            :loading="ispLoading">
            <el-option v-for="item in ispOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="序列号">
          <el-input v-model.trim="searchForm.sns" placeholder="多个英文逗号分隔" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
        <el-form-item>
          <el-button plain type="primary" :disabled="!multipleSelection.length"
            @click="openBatchUpdateDialog">批量维护</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-table :data="tableData" v-loading="loading" border class="table-style"
      @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"></el-table-column>
      <el-table-column prop="supplier_uuid" label="供应商UUID" align="center" width="160">
        <template slot-scope="{ row }">
          {{ supplierMap[row.supplier_uuid] || row.supplier_uuid }}
        </template>
      </el-table-column>
      <el-table-column prop="manager_ip" label="管理IP" align="center" width="140"></el-table-column>
      <el-table-column prop="sn" label="SN" align="center" width="180"></el-table-column>
      <el-table-column prop="isp" label="运营商" align="center" width="80"></el-table-column>
      <el-table-column prop="province" label="省份" align="center" width="100"></el-table-column>
      <el-table-column prop="city" label="城市" align="center" width="100"></el-table-column>
      <el-table-column label="机器状态" align="center" min-width="140">
        <template slot-scope="scope">
          <div class="tag-wrap">
            <el-tag v-for="(t, idx) in parseFaults(scope.row.machine_faults)" :key="t + idx"
              :type="getFaultTypeColor(t)">{{ renderFaultType(t) }}</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="账号状态" align="center" min-width="180">
        <template slot-scope="scope">
          <div class="tag-wrap">
            <el-tag v-for="(t, idx) in parseFaults(scope.row.account_faults)" :key="t + idx"
              :type="getFaultTypeColor(t)">{{ renderFaultType(t) }}</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="故障状态" align="center" width="100">
        <template slot-scope="scope">
          <span :style="{ color: scope.row.status === 0 ? '#F56C6C' : '#67C23A' }">{{ renderStatus(scope.row.status)
          }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="fault_start_time" label="故障开始时间" align="center" width="160">
        <template slot-scope="scope">
          {{ formatTime(scope.row.fault_start_time) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="160" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" @click.stop="openDetail(scope.row)">详情</el-button>
          <span>&nbsp;</span>
          <el-button type="text" @click.stop="openSingleUpdateDialog(scope.row)">维护</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="pagination.page"
      :page-sizes="[10, 20, 50, 100]" :page-size="pagination.page_size" layout="total, sizes, prev, pager, next, jumper"
      :total="pagination.total" class="pagination" />

    <!-- 故障详情弹窗 -->
    <el-dialog title="故障详情" :visible.sync="detailVisible" width="80%" append-to-body>
      <div v-if="detailData">
        <el-descriptions :column="4" size="small" border>
          <el-descriptions-item label="供应商">{{ renderSupplier(detailData.fault.supplier_uuid) }}</el-descriptions-item>
          <el-descriptions-item label="管理IP">{{ detailData.fault.manager_ip }}</el-descriptions-item>
          <el-descriptions-item label="SN">{{ detailData.fault.sn || '-' }}</el-descriptions-item>
          <el-descriptions-item label="省份">{{ detailData.fault.province || '-' }}</el-descriptions-item>
          <el-descriptions-item label="城市">{{ detailData.fault.city || '-' }}</el-descriptions-item>
          <el-descriptions-item label="运营商">{{ detailData.fault.isp || '-' }}</el-descriptions-item>
          <el-descriptions-item label="故障状态">
            <span :style="{ color: detailData.fault.status === 0 ? '#F56C6C' : '#67C23A' }">
              {{ renderStatus(detailData.fault.status) }}
            </span>
          </el-descriptions-item>
          <el-descriptions-item label="故障开始时间">{{ formatTime(detailData.fault.fault_start_time) }}</el-descriptions-item>
          <el-descriptions-item label="故障结束时间">{{ formatTime(detailData.fault.fault_end_time) }}</el-descriptions-item>
          <el-descriptions-item label="机器故障类型">
            <div class="tag-wrap">
              <el-tag v-for="(t, idx) in parseFaults(detailData.fault.machine_faults)" :key="t + idx"
                :type="getFaultTypeColor(t)">{{ renderFaultType(t) }}</el-tag>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="账号故障类型">
            <div class="tag-wrap">
              <el-tag v-for="(t, idx) in parseFaults(detailData.fault.account_faults)" :key="t + idx"
                :type="getFaultTypeColor(t)">{{ renderFaultType(t) }}</el-tag>
            </div>
          </el-descriptions-item>
        </el-descriptions>

        <div style="margin: 16px 0 8px; font-weight: bold">线路详情</div>
        <el-table :data="detailData.line_details || []" border size="small" max-height="350">
          <el-table-column prop="line" label="线路" align="center" width="100" />
          <el-table-column prop="account" label="账号" align="center" width="120" />
          <el-table-column prop="cur_bw" label="实时带宽(Kbps)" align="center" width="120">
            <template slot-scope="scope">{{ toKbps(scope.row.cur_bw) }}</template>
          </el-table-column>
          <el-table-column prop="rated_bw" label="额定带宽(Kbps)" align="center" width="120">
            <template slot-scope="scope">{{ toKbps(scope.row.rated_bw) }}</template>
          </el-table-column>
          <el-table-column prop="loss_rate_ipv4" label="IPv4丢包率(%)" align="center" width="120" />
          <el-table-column prop="loss_rate_ipv6" label="IPv6丢包率(%)" align="center" width="120" />
          <el-table-column prop="retrans_rate" label="重传率(%)" align="center" width="100" />
          <el-table-column prop="dropout_count" label="掉线次数" align="center" width="100" />
          <el-table-column prop="network_quality" label="网络质量分" align="center" width="120" />
          <el-table-column prop="qfc" label="机器健康值" align="center" width="120" />
          <el-table-column prop="dial_status" label="拨号状态" align="center" width="100">
            <template slot-scope="scope">{{ renderDialStatus(scope.row.dial_status) }}</template>
          </el-table-column>
          <el-table-column prop="status" label="线路故障状态" align="center" width="120">
            <template slot-scope="scope">
              <el-tag v-for="(t, idx) in parseFaults(scope.row.fault_types)" :key="t + idx"
                :type="getFaultTypeColor(t)">{{ renderFaultType(t) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="fault_start_time" label="故障开始时间" align="center" width="160">
            <template slot-scope="scope">{{ formatTime(scope.row.fault_start_time) }}</template>
          </el-table-column>
          <el-table-column prop="fault_end_time" label="故障结束时间" align="center" width="160">
            <template slot-scope="scope">{{ formatTime(scope.row.fault_end_time) }}</template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="120" fixed="right">
            <template slot-scope="scope">
              <el-button type="text" @click="openLineUpdateDialog(scope.row)">维护</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailVisible = false">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 批量维护弹窗 -->
    <el-dialog title="批量维护" :visible.sync="batchDialogVisible" width="500px" append-to-body>
      <el-form :model="batchForm" label-width="90px">
        <el-form-item label="维护操作">
          <el-select v-model="batchForm.status" placeholder="请选择">
            <el-option :value="0" label="故障中"></el-option>
            <el-option :value="1" label="已恢复"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input type="textarea" v-model.trim="batchForm.message" :rows="3" placeholder="操作备注"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="batchDialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="batchLoading" @click="handleBatchUpdate">确定</el-button>
      </div>
    </el-dialog>

    <!-- 单条维护弹窗（机器/线路） -->
    <el-dialog :title="singleDialogTitle" :visible.sync="singleDialogVisible" width="500px" append-to-body>
      <el-form :model="singleForm" label-width="90px">
        <el-form-item label="维护对象">
          <el-input v-model="singleForm.targetText" disabled></el-input>
        </el-form-item>
        <el-form-item label="维护操作">
          <el-select v-model="singleForm.status" placeholder="请选择">
            <el-option :value="0" label="故障中"></el-option>
            <el-option :value="1" label="已恢复"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input type="textarea" v-model.trim="singleForm.message" :rows="3" placeholder="操作备注"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="singleDialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="singleLoading" @click="handleSingleUpdate">确定</el-button>
      </div>
    </el-dialog>
  </el-card>
</template>

<script>
import dayjs from 'dayjs'
import http from '../http.js'

export default {
  name: 'small-bw-error-list',
  data() {
    return {
      loading: false,
      tableData: [],
      multipleSelection: [],
      // 查询
      searchForm: {
        ip: '',
        province: '',
        supplier_uuid: '',
        isp: '',
        sns: '',
        status: 0,
      },
      areaOptions: [],
      provinceOptions: [],
      supplierOptions: [],
      ispOptions: [],
      areaLoading: false,
      provinceLoading: false,
      supplierLoading: false,
      singleLoading: false,
      ispLoading: false,
      pagination: {
        total: 0,
        page: 1,
        page_size: 20,
      },
      // 详情
      detailVisible: false,
      detailData: null,
      // 批量维护
      batchDialogVisible: false,
      batchForm: {
        status: 1,
        message: ''
      },
      batchLoading: false,
      // 单条维护
      singleDialogVisible: false,
      singleDialogTitle: '维护',
      singleForm: {
        ids: [],
        status: 1,
        message: '',
        targetText: '',
        type: 'machine', // machine | line
        lineId: null,
      },
      // 本地映射
      supplierMap: {},
    }
  },
  created() {
    this.getList()
    this.loadSupplierOptions()
  },
  methods: {
    // 列表
    async getList() {
      this.loading = true
      try {
        const params = {
          page: this.pagination.page,
          page_size: this.pagination.page_size,
        }
        if (this.searchForm.ip) {
          params.manager_ips = this.searchForm.ip
        }
        if (this.searchForm.sns) {
          params.sns = this.searchForm.sns
        }
        if (this.searchForm.province) params.province = this.searchForm.province
        if (this.searchForm.supplier_uuid) params.supplier_uuid = this.searchForm.supplier_uuid
        if (this.searchForm.isp) params.isp = this.searchForm.isp
        if (this.searchForm.status !== '' && this.searchForm.status !== null && this.searchForm.status !== undefined) params.status = Number(this.searchForm.status)

        const res = await http.getFaultsList(params)
        this.tableData = res?.data?.items || []
        this.pagination.total = res?.data?.total || 0
      } catch (e) {
        console.error(e)
      } finally {
        this.loading = false
      }
    },
    handleSizeChange(val) {
      this.pagination.page_size = val
      this.pagination.page = 1
      this.getList()
    },
    handleCurrentChange(val) {
      this.pagination.page = val
      this.getList()
    },
    handleSearch() {
      this.pagination.page = 1
      this.getList()
    },
    handleReset() {
      this.searchForm = { ip: '', province: '', supplier_uuid: '', isp: '', sns: '', status: 0 }
      this.pagination.page = 1
      this.getList()
    },
    handleSelectionChange(val) {
      this.multipleSelection = JSON.parse(JSON.stringify(val))
    },
    async loadProvinceOptions() {
      if (this.provinceOptions.length) return
      this.provinceLoading = true
      try {
        const res = await http.getProvinceParams()
        this.provinceOptions = (res?.data || []).map(it => ({ label: it.name || it.label || it, value: it.value || it.code || it }))
      } finally { this.provinceLoading = false }
    },
    async loadSupplierOptions() {
      if (this.supplierOptions.length) return
      this.supplierLoading = true
      try {
        const res = await http.getSupplierParams()
        this.supplierOptions = (res?.data || []).map(it => ({ label: it.supplier_name, value: it.uuid }))
        this.supplierMap = {};
        (res?.data || []).forEach(it => { const key = it.uuid; this.supplierMap[key] = `${it.supplier_name} (${key})` })
      } finally { this.supplierLoading = false }
    },
    async loadIspOptions() {
      if (this.ispOptions.length) return
      this.ispLoading = true
      try {
        const res = await http.getIspParams()
        this.ispOptions = (res?.data || []).map(it => ({ label: it.name || it.label || it, value: it.value || it.code || it }))
      } finally { this.ispLoading = false }
    },
    renderSupplier(uuid) {
      return this.supplierMap[uuid] || uuid || '-'
    },
    parseFaults(str) {
      if (!str) return []
      return String(str).split(',').filter(Boolean).map(s => Number(s))
    },
    renderFaultType(code) {
      const map = {
        [-1]: '未知',
        [0]: '正常状态',
        [1]: '跑超上限',
        [2]: '网关异常',
        [3]: '健康值异常',
        [4]: '网络异常',
        [5]: '机器失联',
        [6]: '拨号失败',
        [7]: '未拨号',
        [8]: '频繁掉线',
        [9]: 'NAT类型异常',
        [10]: '利用率低',
      }
      return map[code] || code
    },
    getFaultTypeColor(code) {
      const colorMap = {
        [-1]: 'info',      // 未知 - 灰色
        [0]: 'success',    // 正常状态 - 绿色
        [1]: 'danger',     // 跑超上限 - 红色
        [2]: 'warning',    // 网关异常 - 黄色
        [3]: 'warning',    // 健康值异常 - 黄色
        [4]: 'danger',     // 网络异常 - 红色
        [5]: 'danger',     // 机器失联 - 红色
        [6]: 'warning',    // 拨号失败 - 黄色
        [7]: 'info',       // 未拨号 - 灰色
        [8]: 'warning',    // 频繁掉线 - 黄色
        [9]: 'warning',    // NAT类型异常 - 黄色
        [10]: 'info',      // 利用率低 - 灰色
      }
      return colorMap[code] || 'info'
    },
    renderStatus(v) { return { [-1]: '全部', [0]: '故障中', [1]: '已恢复' }[v] || v },
    formatTime(ts) { if (!ts) return '-'; return dayjs(ts * 1000).format('YYYY-MM-DD HH:mm:ss') },
    toKbps(bps) { if (bps === 0 || bps) return Math.round(bps / 1000); return '-' },
    renderDialStatus(v) { return { "-1": '未拨号', 0: '拨号失败', 1: '拨号成功' }[v] || v },

    // 详情
    async openDetail(row) {
      try {
        const res = await http.getFaultDetail(row.id)
        this.detailData = res?.data || null
        this.detailVisible = true
      } catch (e) { console.error(e) }
    },

    // 批量维护
    openBatchUpdateDialog() {
      this.batchDialogVisible = true
      this.batchForm = { status: 1, message: '' }
    },
    async handleBatchUpdate() {
      if (!this.multipleSelection.length) return this.$message.warning('请先选择数据！')
      try {
        this.batchLoading = true
        const ids = this.multipleSelection.map(it => it.id)
        const params = { ids, status: this.batchForm.status, operator: 'admin', message: this.batchForm.message }
        const res = await http.updateFaultStatus(params)
        if (res && (res.code === 100000 || res.code === 200)) {
          this.$message.success('维护成功')
          this.batchDialogVisible = false
          this.getList()
        }
      } catch (e) { console.error(e) } finally { this.batchLoading = false }
    },

    // 单条维护（机器）
    openSingleUpdateDialog(row) {
      this.singleDialogTitle = '维护-机器'
      this.singleForm = { ids: [row.id], status: 1, message: '', targetText: `${row.manager_ip} (${this.renderSupplier(row.supplier_uuid)})`, type: 'machine', lineId: null }
      this.singleDialogVisible = true
    },
    // 单条维护（线路）
    openLineUpdateDialog(lineRow) {
      this.singleDialogTitle = '维护-线路'
      this.singleForm = { ids: [lineRow.id], status: 1, message: '', targetText: `${lineRow.manager_ip}-${lineRow.line}`, type: 'line', lineId: lineRow.id }
      this.singleDialogVisible = true
    },
    async handleSingleUpdate() {
      try {
        this.singleLoading = true
        const params = { ids: this.singleForm.ids, status: this.singleForm.status, operator: 'admin', message: this.singleForm.message }
        const api = this.singleForm.type === 'line' ? http.updateLineFaultStatus : http.updateFaultStatus
        const res = await api(params)
        if (res && (res.code === 100000 || res.code === 200)) {
          this.$message.success('维护成功')
          this.singleDialogVisible = false
          if (this.singleForm.type === 'line') {
            // 刷新详情
            // 若有detailData则刷新
            if (this.detailData && this.detailData.fault && this.detailData.fault.id) {
              const r = await http.getFaultDetail(this.detailData.fault.id)
              this.detailData = r?.data || null
            }
          } else {
            this.getList()
          }
        }
      } catch (e) { console.error(e) } finally { this.singleLoading = false }
    },
  }
}
</script>

<style scoped lang="scss">
.flex-container {
  display: flex;
  justify-content: space-between;
}

.tag-wrap {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.pagination {
  margin-top: 12px;
  text-align: right;
}
</style>
